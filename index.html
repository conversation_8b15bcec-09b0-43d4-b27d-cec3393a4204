<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通辽法学会门户网站 - 原型方案选择</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800 mb-2">通辽法学会门户网站</h1>
                <p class="text-xl text-gray-600">原型设计方案展示</p>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">选择您喜欢的设计方案</h2>
            <p class="text-lg text-gray-600">我们为您准备了四套不同风格的首页设计方案</p>
        </div>

        <!-- Design Options -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-8 max-w-full mx-auto">
            <!-- Blue Theme - Main Recommendation -->
            <div
                class="xl:col-span-2 card-hover bg-white rounded-lg shadow-xl overflow-hidden border-2 border-blue-500">
                <div class="h-64 bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                    <i class="fas fa-gavel text-white text-8xl"></i>
                </div>
                <div class="p-8">
                    <h3 class="text-3xl font-bold text-gray-800 mb-4">蓝色主题方案 <span
                            class="text-lg bg-blue-100 text-blue-600 px-3 py-1 rounded-full">主推荐</span></h3>
                    <p class="text-gray-600 mb-6 text-lg">采用专业的蓝色作为主色调，体现法学的理性和专业性，符合现代政府门户网站设计趋势。提供三个不同风格的版本供您选择。</p>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex space-x-2">
                            <div class="w-5 h-5 bg-blue-500 rounded-full"></div>
                            <div class="w-5 h-5 bg-blue-700 rounded-full"></div>
                            <div class="w-5 h-5 bg-gray-800 rounded-full"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-3">
                        <a href="blue-theme/index.html"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors text-center font-medium text-sm">
                            蓝色方案 1
                        </a>
                        <a href="blue-theme/index2.html"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors text-center font-medium text-sm">
                            蓝色方案 2
                        </a>
                        <a href="blue-theme/index3.html"
                            class="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-4 py-3 rounded-lg transition-colors text-center font-medium text-sm">
                            新闻门户版
                        </a>
                    </div>
                </div>
            </div>

            <!-- Red Theme -->
            <div class="card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-br from-red-500 to-red-700 flex items-center justify-center">
                    <i class="fas fa-balance-scale text-white text-6xl"></i>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-3">红色主题方案</h3>
                    <p class="text-gray-600 mb-4 text-sm">采用庄重的红色作为主色调，体现法律的权威性和严肃性。</p>
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-2">
                            <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                            <div class="w-4 h-4 bg-red-700 rounded-full"></div>
                            <div class="w-4 h-4 bg-gray-800 rounded-full"></div>
                        </div>
                        <a href="red-theme/index.html"
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                            查看方案
                        </a>
                    </div>
                </div>
            </div>

            <!-- Green Theme -->
            <div class="card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
                    <i class="fas fa-university text-white text-6xl"></i>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-3">绿色主题方案</h3>
                    <p class="text-gray-600 mb-4 text-sm">采用现代的绿色作为主色调，体现法治建设的生机与活力。</p>
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-2">
                            <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                            <div class="w-4 h-4 bg-green-700 rounded-full"></div>
                            <div class="w-4 h-4 bg-gray-800 rounded-full"></div>
                        </div>
                        <a href="green-theme/index.html"
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                            查看方案
                        </a>
                    </div>
                </div>
            </div>

            <!-- Purple Theme -->
            <div class="card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="h-48 bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center">
                    <i class="fas fa-scales text-white text-6xl"></i>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-3">紫色主题方案</h3>
                    <p class="text-gray-600 mb-4 text-sm">采用优雅的紫色作为主色调，体现法学的智慧与权威。</p>
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-2">
                            <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                            <div class="w-4 h-4 bg-purple-700 rounded-full"></div>
                            <div class="w-4 h-4 bg-gray-800 rounded-full"></div>
                        </div>
                        <a href="purple-theme/index.html"
                            class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                            查看方案
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="mt-16 bg-white rounded-lg shadow-lg p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">设计特色</h3>
            <div class="grid md:grid-cols-4 gap-6">
                <div class="text-center">
                    <i class="fas fa-mobile-alt text-3xl text-blue-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">响应式设计</h4>
                    <p class="text-sm text-gray-600">适配各种设备屏幕</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-tachometer-alt text-3xl text-green-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">快速加载</h4>
                    <p class="text-sm text-gray-600">优化的性能表现</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-universal-access text-3xl text-purple-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">无障碍访问</h4>
                    <p class="text-sm text-gray-600">符合政府网站标准</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-search text-3xl text-orange-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-800 mb-2">SEO优化</h4>
                    <p class="text-sm text-gray-600">搜索引擎友好</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 通辽法学会门户网站原型设计</p>
        </div>
    </footer>
</body>

</html>