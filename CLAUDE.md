# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a website prototype project for the Tongliao Law Association (通辽法学会门户网站) featuring multiple design themes. The project consists of a theme selection page and four different color-themed website implementations.

## Architecture

### Project Structure
```
/
├── index.html              # Main theme selection page
├── blue-theme/            # Blue theme implementation (primary/recommended)
│   ├── index.html         # Version 1 - Standard layout
│   ├── index2.html        # Version 2 - Optimized with modular architecture
│   ├── index3.html        # Version 3 - News portal layout
│   ├── list.html          # List page template
│   ├── view.html          # Detail view template
│   ├── css/main.css       # Modular CSS with custom properties
│   ├── js/main.js         # Modular JavaScript architecture
│   ├── images/            # Theme-specific images
│   └── README.md          # Blue theme V2 optimization documentation
├── purple-theme/          # Purple theme (complete implementation)
│   ├── index.html, index2.html  # Multiple layout versions
│   ├── list.html, view.html     # Page templates
│   └── README.md          # Documentation
├── green-theme/           # Green theme (enhanced with AOS animations)
│   ├── index.html         # Single version with AOS scroll animations
│   └── OPTIMIZATION_SUMMARY.md  # Comprehensive optimization documentation
├── red-theme/             # Red theme (basic implementation)
│   └── index.html         # Single version
└── CLAUDE.md              # This file - project guidance
```

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Frameworks**: Tailwind CSS (via CDN)
- **Libraries**: 
  - Font Awesome 6.0+ for icons
  - Swiper.js 11 for carousels/sliders
  - AOS (Animate On Scroll) in green theme
  - Google Fonts (Noto Sans SC) for Chinese typography
- **Architecture**: Modular CSS with custom properties, modular JavaScript
- **Build Process**: None - pure static files served directly

### Theme Architecture
Each theme follows a consistent structure:
- **CSS Variables**: All themes use CSS custom properties for colors and dimensions
- **Modular JavaScript**: Each theme implements modules for SearchModule, SwiperModule, AnimationModule, etc.
- **Responsive Design**: All themes are mobile-first and responsive
- **Component-based CSS**: Reusable classes like `.card-hover`, `.nav-link`, `.section-card`

## Key Design Patterns

### CSS Organization
- CSS custom properties defined in `:root` for consistent theming
- Modular class naming: `.search-container`, `.nav-link`, `.news-item`
- Hover effects and transitions unified across components
- Glass effects and gradient backgrounds for modern aesthetics

### JavaScript Architecture
```javascript
// Module pattern used throughout
const SearchModule = {
    init() { /* initialization */ },
    bindEvents() { /* event binding */ }
};

const SwiperModule = {
    init() { /* swiper setup */ },
    destroy() { /* cleanup */ }
};
```

### External Dependencies
All themes rely on CDN-hosted libraries (no local package management):
- Tailwind CSS: `https://cdn.tailwindcss.com`
- Font Awesome: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css`
- Swiper: `https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css`
- Google Fonts: `https://fonts.googleapis.com/css2?family=Noto+Sans+SC` (in index3.html)
- AOS: `https://unpkg.com/aos@2.3.1/dist/aos.css` (green theme only)

## Development Guidelines

### No Build Process
This project uses **pure static files** with **no build system**:
- No package.json, webpack, or bundlers
- No npm scripts or development commands  
- Files can be opened directly in browser or served via simple HTTP server
- All dependencies are CDN-hosted
- Changes are immediately visible by refreshing the browser

### File Organization
- Always maintain the theme directory structure
- CSS should be in `css/main.css` with external stylesheets referenced in HTML
- JavaScript should be in `js/main.js` with modular architecture
- Images should be in theme-specific `images/` directories

### Code Style
- Use semantic HTML5 elements
- Follow the existing CSS custom property naming convention
- Maintain the modular JavaScript pattern
- Use consistent class naming across themes

### Browser Support
- Chrome 60+
- Firefox 60+ (55+ in green theme)
- Safari 12+
- Edge 79+

## Theme Development Patterns

### Theme Maturity Levels
- **Blue Theme**: Most mature with 3 versions (standard, optimized, news portal)
- **Purple Theme**: Complete implementation with all page templates
- **Green Theme**: Enhanced with AOS animations and comprehensive optimization
- **Red Theme**: Basic implementation, single version

### Version Strategy
- **index.html**: Standard layout version
- **index2.html**: Optimized version with modular architecture
- **index3.html**: News portal specialized layout (blue theme only)

## Common Tasks

### Adding New Themes
1. Create new theme directory following existing structure
2. Copy base files from an existing theme
3. Update CSS custom properties for new color scheme
4. Update theme-specific images
5. Add theme option to main `index.html`

### Modifying Existing Themes
1. CSS changes go in `css/main.css`
2. JavaScript changes go in `js/main.js`
3. Maintain the modular architecture
4. Test responsive design across device sizes

### Working with Components
- Navigation: Uses dropdown menus with hover effects
- Cards: Implement `.card-hover` class for consistent animations
- Search: Centralized in `SearchModule` with configurable options
- Sliders: Managed by `SwiperModule` with performance optimization

## Development Commands

### Local Development
Since this is a static website with no build process:

```bash
# Serve files locally (choose one method)
python -m http.server 8000        # Python 3
python -m SimpleHTTPServer 8000   # Python 2
npx serve .                       # Node.js serve package
php -S localhost:8000             # PHP built-in server
```

### File Editing Workflow
1. Edit HTML/CSS/JS files directly
2. Refresh browser to see changes
3. Test across different browsers and device sizes
4. No compilation or build step required

### Key Documentation Files
- `/blue-theme/README.md` - Blue theme V2 optimization details
- `/purple-theme/README.md` - Purple theme documentation
- `/green-theme/OPTIMIZATION_SUMMARY.md` - Comprehensive optimization guide