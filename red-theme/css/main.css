/* 主要样式文件 */
:root {
    --primary-blue: #991b1b;
    --secondary-blue: #dc2626;
    --light-blue: #f87171;
    --hover-blue: #b91c1c;
    --text-gray: #374151;
    --light-gray: #f9fafb;
    --border-gray: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-gray);
}

/* 渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--light-blue) 100%);
    position: relative;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2px, transparent 0);
    background-size: 100px 100px;
    pointer-events: none;
}

/* 玻璃效果 */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 文字阴影 */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 下拉菜单 */
.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu {
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Swiper 轮播图样式 */
.swiper {
    width: 100%;
    height: 100%;
    position: relative;
}

.swiper-wrapper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.swiper-slide > div {
    width: 100%;
    height: 100%;
    position: relative;
}

.swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Swiper 导航按钮 */
.swiper-button-next,
.swiper-button-prev {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    margin-top: -20px !important;
    transition: all 0.3s ease !important;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: rgba(255, 255, 255, 1) !important;
    transform: scale(1.1) !important;
}

.swiper-button-next::after, 
.swiper-button-prev::after {
    font-size: 16px !important;
    color: #374151 !important;
    font-weight: bold !important;
}

.swiper-button-disabled {
    opacity: 0.5 !important;
}

/* Swiper 分页器 */
.swiper-pagination {
    bottom: 15px !important;
    text-align: center !important;
}

.swiper-pagination-bullet {
    width: 10px !important;
    height: 10px !important;
    background: rgba(255, 255, 255, 0.6) !important;
    opacity: 1 !important;
    margin: 0 4px !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

.swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.3) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* 搜索框样式 */
.search-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 9999px;
    padding: 8px 16px;
    min-width: 280px;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-container:hover {
    background: rgba(255, 255, 255, 1);
}

.search-container.focused {
    ring: 2px;
    ring-color: rgba(59, 130, 246, 0.6);
}

.search-input {
    flex: 1;
    background: transparent;
    color: var(--text-gray);
    border: none;
    outline: none;
    font-size: 14px;
}

.search-input::placeholder {
    color: #6b7280;
}

.search-button {
    width: 40px;
    height: 40px;
    margin-left: 8px;
    background: var(--secondary-blue);
    color: white;
    border-radius: 50%;
    padding: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    border: none;
    cursor: pointer;
}

.search-button:hover {
    background: var(--hover-blue);
    transform: scale(1.1);
}

.search-button:active {
    transform: scale(0.95);
}

/* 导航栏样式 */
.nav-link {
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 18px;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.nav-link:hover {
    color: #bfdbfe;
    background: rgba(29, 78, 216, 0.8);
}

/* 新闻列表项 */
.news-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: white;
    border-radius: 12px;
    border: 1px solid var(--border-gray);
    overflow: hidden;
    padding: 12px;
    transition: all 0.3s ease;
}

.news-item:hover {
    background: #f9fafb;
}

.news-dot {
    width: 8px;
    height: 8px;
    background: #93c5fd;
    border-radius: 50%;
    flex-shrink: 0;
}

.news-link {
    color: var(--text-gray);
    text-decoration: none;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.news-link:hover {
    color: var(--secondary-blue);
}

.news-date {
    font-size: 14px;
    color: #6b7280;
    flex-shrink: 0;
}

/* 栏目卡片 */
.section-card {
    background: white;
    border-radius: 12px;
    border: 1px solid var(--border-gray);
    overflow: hidden;
    transition: all 0.3s ease;
}

.section-card:hover {
    box-shadow: var(--shadow-lg);
}

.section-header {
    padding: 16px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    display: flex;
    align-items: center;
}

.section-content {
    padding: 24px;
}

/* 专题卡片 */
.topic-card {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    height: 160px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.topic-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.topic-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.topic-content {
    text-align: center;
    color: white;
}

.topic-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.topic-card:hover .topic-title {
    transform: scale(1.1);
}

.topic-description {
    font-size: 14px;
    opacity: 0.9;
}

.topic-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
    opacity: 0.7;
}

/* 统计卡片 */
.stat-card {
    text-align: center;
    padding: 16px;
    border-radius: 8px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

/* 视频卡片 */
.video-thumbnail {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.video-thumbnail:hover .video-overlay {
    background: rgba(0, 0, 0, 0.6);
}

.play-icon {
    color: white;
    font-size: 24px;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover .play-icon {
    transform: scale(1.2);
}

/* 快速链接 */
.quick-link {
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 100px;
}

.quick-link i {
    font-size: 20px;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.quick-link:hover i {
    transform: scale(1.1);
}

.quick-link span {
    font-size: 14px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-container {
        min-width: 240px;
    }
    
    .nav-link {
        font-size: 16px;
        padding: 6px 12px;
    }
    
    .topic-title {
        font-size: 20px;
    }
    
    .stat-number {
        font-size: 20px;
    }
}

@media (max-width: 640px) {
    .search-container {
        min-width: 200px;
    }
    
    .section-content {
        padding: 16px;
    }
    
    .topic-card {
        height: 120px;
    }
    
    .topic-title {
        font-size: 18px;
    }
}

/* 工具类 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--secondary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 过渡动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 