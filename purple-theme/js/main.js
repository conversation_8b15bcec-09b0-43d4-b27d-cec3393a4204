/**
 * 通辽市法学会网站主要JavaScript文件
 */

// 全局配置
const CONFIG = {
    swiper: {
        autoplayDelay: 5000,
        speed: 600,
        pauseOnMouseEnter: true
    },
    search: {
        minLength: 1,
        placeholder: '搜索网站内容...'
    }
};

// DOM工具函数
const DOM = {
    ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    },
    
    $(selector) {
        return document.querySelector(selector);
    },
    
    $$(selector) {
        return document.querySelectorAll(selector);
    }
};

// 搜索功能模块
const SearchModule = {
    init() {
        this.bindEvents();
        this.setupSearchContainer();
    },

    bindEvents() {
        const searchInput = DOM.$('.search-input');
        const searchButton = DOM.$('.search-button');
        
        if (!searchInput || !searchButton) return;

        // 搜索按钮点击事件
        searchButton.addEventListener('click', () => this.performSearch());
        
        // 回车键搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });
        
        // 搜索框焦点效果
        searchInput.addEventListener('focus', () => this.onSearchFocus());
        searchInput.addEventListener('blur', () => this.onSearchBlur());
    },

    setupSearchContainer() {
        const container = DOM.$('.search-container');
        if (container) {
            container.addEventListener('mouseenter', () => {
                container.style.background = 'rgba(255, 255, 255, 1)';
            });
            
            container.addEventListener('mouseleave', () => {
                if (!DOM.$('.search-input:focus')) {
                    container.style.background = 'rgba(255, 255, 255, 0.95)';
                }
            });
        }
    },

    onSearchFocus() {
        const container = DOM.$('.search-container');
        if (container) {
            container.classList.add('focused');
        }
    },

    onSearchBlur() {
        const container = DOM.$('.search-container');
        if (container) {
            container.classList.remove('focused');
            container.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    },

    performSearch() {
        const searchInput = DOM.$('.search-input');
        if (!searchInput) return;

        const searchTerm = searchInput.value.trim();
        if (searchTerm.length >= CONFIG.search.minLength) {
            console.log('搜索内容:', searchTerm);
            alert(`正在搜索: ${searchTerm}`);
        }
    }
};

// 轮播图模块
const SwiperModule = {
    instance: null,

    init() {
        if (typeof Swiper === 'undefined') {
            console.warn('Swiper library not loaded');
            return;
        }

        this.initSwiper();
    },

    initSwiper() {
        const swiperContainer = DOM.$('.swiper');
        if (!swiperContainer) return;

        this.instance = new Swiper('.swiper', {
            slidesPerView: 1,
            spaceBetween: 0,
            centeredSlides: true,
            
            autoplay: {
                delay: CONFIG.swiper.autoplayDelay,
                disableOnInteraction: false,
                pauseOnMouseEnter: CONFIG.swiper.pauseOnMouseEnter,
            },
            
            loop: true,
            effect: 'slide',
            speed: CONFIG.swiper.speed,
            
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                type: 'bullets',
            },
            
            touchRatio: 1,
            touchAngle: 45,
            
            breakpoints: {
                640: { slidesPerView: 1 },
                768: { slidesPerView: 1 },
                1024: { slidesPerView: 1 }
            }
        });
    },

    destroy() {
        if (this.instance) {
            this.instance.destroy(true, true);
            this.instance = null;
        }
    }
};

// 平滑滚动模块
const SmoothScrollModule = {
    init() {
        this.bindEvents();
    },

    bindEvents() {
        const anchors = DOM.$$('a[href^="#"]');
        anchors.forEach(anchor => {
            anchor.addEventListener('click', (e) => this.handleAnchorClick(e));
        });
    },

    handleAnchorClick(e) {
        e.preventDefault();
        const targetId = e.currentTarget.getAttribute('href');
        const target = DOM.$(targetId);
        
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
};

// 主应用模块
const App = {
    init() {
        console.log('通辽市法学会网站初始化中...');
        
        SearchModule.init();
        SwiperModule.init();
        SmoothScrollModule.init();
        
        console.log('网站初始化完成');
    },

    destroy() {
        SwiperModule.destroy();
        console.log('应用已清理');
    }
};

// 页面加载完成后初始化应用
DOM.ready(() => {
    App.init();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    App.destroy();
});
