/* 通辽市法学会 - 绿色主题优化版 */
:root {
    /* 主色调 - 绿色系 */
    --primary-green: #0f5132;
    --secondary-green: #198754;
    --light-green: #20c997;
    --accent-green: #25d366;
    --hover-green: #0a3d26;
    --soft-green: #d1e7dd;

    /* 辅助色彩 */
    --gold-accent: #ffc107;
    --warm-gold: #ffb700;
    --deep-gold: #cc9500;

    /* 中性色 */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-light: #dee2e6;
    --border-medium: #ced4da;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 50%, var(--light-green) 100%);
    --gradient-warm: linear-gradient(135deg, var(--secondary-green) 0%, var(--accent-green) 100%);
    --gradient-gold: linear-gradient(135deg, var(--warm-gold) 0%, var(--gold-accent) 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(15, 81, 50, 0.9) 0%, rgba(25, 135, 84, 0.8) 100%);

    /* 动画时长 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;

    /* 边框圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* 基础样式重置与优化 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-light);
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 选择文本样式 */
::selection {
    background-color: var(--light-green);
    color: white;
}

::-moz-selection {
    background-color: var(--light-green);
    color: white;
}

/* 增强渐变背景系统 */
.gradient-bg {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25px 25px, rgba(255,255,255,0.08) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(255,255,255,0.08) 2px, transparent 0);
    background-size: 100px 100px;
    pointer-events: none;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

.gradient-warm {
    background: var(--gradient-warm);
    position: relative;
}

.gradient-gold {
    background: var(--gradient-gold);
    position: relative;
}

/* 增强玻璃效果 */
.glass-effect {
    backdrop-filter: blur(16px) saturate(180%);
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
}

.glass-dark {
    backdrop-filter: blur(16px) saturate(180%);
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 增强卡片悬停效果 */
.card-hover {
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow) ease;
    pointer-events: none;
}

.card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.card-hover:hover::before {
    left: 100%;
}

.card-hover:active {
    transform: translateY(-2px) scale(1.01);
    transition: all var(--transition-fast) ease;
}

/* 特殊卡片效果 */
.card-glow {
    position: relative;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.card-glow::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-normal) ease;
}

.card-glow:hover::after {
    opacity: 1;
}

/* 文字阴影 */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 下拉菜单 */
.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu {
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Swiper 轮播图样式 */
.swiper {
    width: 100%;
    height: 100%;
    position: relative;
}

.swiper-wrapper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.swiper-slide > div {
    width: 100%;
    height: 100%;
    position: relative;
}

.swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Swiper 导航按钮 */
.swiper-button-next,
.swiper-button-prev {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    margin-top: -20px !important;
    transition: all 0.3s ease !important;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: rgba(255, 255, 255, 1) !important;
    transform: scale(1.1) !important;
}

.swiper-button-next::after, 
.swiper-button-prev::after {
    font-size: 16px !important;
    color: #374151 !important;
    font-weight: bold !important;
}

.swiper-button-disabled {
    opacity: 0.5 !important;
}

/* Swiper 分页器 */
.swiper-pagination {
    bottom: 15px !important;
    text-align: center !important;
}

.swiper-pagination-bullet {
    width: 10px !important;
    height: 10px !important;
    background: rgba(255, 255, 255, 0.6) !important;
    opacity: 1 !important;
    margin: 0 4px !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

.swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.3) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* 增强搜索框样式 */
.search-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(16px) saturate(180%);
    border-radius: var(--radius-full);
    padding: 6px 20px;
    min-width: 320px;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow) ease;
}

.search-container:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--light-green);
    box-shadow: var(--shadow-xl);
    transform: translateY(-1px);
}

.search-container:hover::before {
    left: 100%;
}

.search-container.focused {
    border-color: var(--secondary-green);
    box-shadow: 0 0 0 4px rgba(25, 135, 84, 0.1), var(--shadow-xl);
    background: rgba(255, 255, 255, 1);
}

.search-input {
    flex: 1;
    background: transparent;
    color: var(--text-primary);
    border: none;
    outline: none;
    font-size: 15px;
    font-weight: 500;
    padding: 12px 0;
}

.search-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.search-button {
    width: 44px;
    height: 44px;
    margin-left: 12px;
    background: var(--gradient-warm);
    color: white;
    border-radius: var(--radius-full);
    padding: 0;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.search-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all var(--transition-normal) ease;
    transform: translate(-50%, -50%);
}

.search-button:hover {
    background: var(--gradient-primary);
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.search-button:hover::before {
    width: 100%;
    height: 100%;
}

.search-button:active {
    transform: scale(0.95) rotate(0deg);
    transition: all var(--transition-fast) ease;
}

/* 增强导航栏样式 */
.nav-link {
    color: white;
    padding: 12px 20px;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal) ease;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:active {
    transform: translateY(0);
    transition: all var(--transition-fast) ease;
}

.nav-link i {
    transition: transform var(--transition-normal) ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}

/* 增强新闻列表项样式 */
.news-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
    padding: 16px;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin-bottom: 2px;
}

.news-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-warm);
    transform: scaleY(0);
    transition: transform var(--transition-normal) ease;
    transform-origin: bottom;
}

.news-item:hover {
    background: var(--soft-green);
    border-color: var(--light-green);
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.news-item:hover::before {
    transform: scaleY(1);
}

.news-dot {
    width: 10px;
    height: 10px;
    background: var(--gradient-warm);
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.1);
    transition: all var(--transition-normal) ease;
}

.news-item:hover .news-dot {
    transform: scale(1.2);
    box-shadow: 0 0 0 6px rgba(25, 135, 84, 0.2);
}

.news-link {
    color: var(--text-primary);
    text-decoration: none;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
    transition: color var(--transition-normal) ease;
}

.news-link:hover {
    color: var(--secondary-green);
}

.news-date {
    font-size: 13px;
    color: var(--text-secondary);
    flex-shrink: 0;
    font-weight: 500;
    padding: 4px 8px;
    background: var(--bg-light);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal) ease;
}

.news-item:hover .news-date {
    background: var(--light-green);
    color: white;
}

/* 栏目卡片 */
.section-card {
    background: white;
    border-radius: 12px;
    border: 1px solid var(--border-gray);
    overflow: hidden;
    transition: all 0.3s ease;
}

.section-card:hover {
    box-shadow: var(--shadow-lg);
}

.section-header {
    padding: 16px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    display: flex;
    align-items: center;
}

.section-content {
    padding: 24px;
}

/* 专题卡片 */
.topic-card {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    height: 160px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.topic-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.topic-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.topic-content {
    text-align: center;
    color: white;
}

.topic-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.topic-card:hover .topic-title {
    transform: scale(1.1);
}

.topic-description {
    font-size: 14px;
    opacity: 0.9;
}

.topic-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
    opacity: 0.7;
}

/* 统计卡片 */
.stat-card {
    text-align: center;
    padding: 16px;
    border-radius: 8px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

/* 视频卡片 */
.video-thumbnail {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.video-thumbnail:hover .video-overlay {
    background: rgba(0, 0, 0, 0.6);
}

.play-icon {
    color: white;
    font-size: 24px;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover .play-icon {
    transform: scale(1.2);
}

/* 快速链接 */
.quick-link {
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 100px;
}

.quick-link i {
    font-size: 20px;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.quick-link:hover i {
    transform: scale(1.1);
}

.quick-link span {
    font-size: 14px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-container {
        min-width: 240px;
    }
    
    .nav-link {
        font-size: 16px;
        padding: 6px 12px;
    }
    
    .topic-title {
        font-size: 20px;
    }
    
    .stat-number {
        font-size: 20px;
    }
}

@media (max-width: 640px) {
    .search-container {
        min-width: 200px;
    }
    
    .section-content {
        padding: 16px;
    }
    
    .topic-card {
        height: 120px;
    }
    
    .topic-title {
        font-size: 18px;
    }
}

/* 工具类 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--secondary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 增强过渡动画系统 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 个性化装饰元素 */
.decorative-line {
    position: relative;
    overflow: hidden;
}

.decorative-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-green), var(--light-green), var(--primary-green));
    transform: translateY(-50%) scaleX(0);
    animation: expandLine 2s ease-out forwards;
}

@keyframes expandLine {
    to {
        transform: translateY(-50%) scaleX(1);
    }
}

/* 法学会专属样式 */
.law-badge {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal) ease;
}

.law-badge::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: var(--gradient-gold);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-normal) ease;
}

.law-badge:hover::before {
    opacity: 1;
}

.law-badge:hover {
    transform: scale(1.1) rotate(5deg);
}

/* 通辽特色元素 */
.tongliao-pattern {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(25, 135, 84, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(32, 201, 151, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(255, 193, 7, 0.05) 0%, transparent 50%);
    background-size: 200px 200px, 150px 150px, 100px 100px;
    background-position: 0 0, 50px 50px, 100px 0;
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% {
        background-position: 0 0, 50px 50px, 100px 0;
    }
    100% {
        background-position: 200px 200px, 250px 250px, 300px 200px;
    }
}

/* 智能提示框 */
.smart-tooltip {
    position: relative;
    cursor: help;
}

.smart-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) ease;
    z-index: 1000;
}

.smart-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 5px solid transparent;
    border-top-color: var(--text-primary);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) ease;
}

.smart-tooltip:hover::after,
.smart-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* 数据可视化元素 */
.data-chart {
    position: relative;
    height: 200px;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.data-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.chart-bar {
    display: flex;
    align-items: end;
    height: 100%;
    gap: 8px;
}

.chart-bar-item {
    flex: 1;
    background: var(--gradient-warm);
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    min-height: 20px;
    transition: all var(--transition-normal) ease;
    position: relative;
    animation: chartGrow 1s ease-out forwards;
    transform-origin: bottom;
    transform: scaleY(0);
}

.chart-bar-item:nth-child(1) { animation-delay: 0.1s; }
.chart-bar-item:nth-child(2) { animation-delay: 0.2s; }
.chart-bar-item:nth-child(3) { animation-delay: 0.3s; }
.chart-bar-item:nth-child(4) { animation-delay: 0.4s; }
.chart-bar-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes chartGrow {
    to {
        transform: scaleY(1);
    }
}

.chart-bar-item:hover {
    background: var(--gradient-primary);
    transform: scaleY(1.1);
}

/* 时间轴样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal) ease;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -37px;
    top: 25px;
    width: 12px;
    height: 12px;
    background: var(--secondary-green);
    border-radius: 50%;
    border: 3px solid var(--bg-white);
    box-shadow: var(--shadow-md);
}

.timeline-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
}

/* 成就徽章 */
.achievement-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--gradient-gold);
    color: white;
    border-radius: var(--radius-full);
    font-size: 14px;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal) ease;
}

.achievement-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.achievement-badge i {
    font-size: 16px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}