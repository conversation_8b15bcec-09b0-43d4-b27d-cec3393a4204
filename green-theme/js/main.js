/**
 * 通辽市法学会网站 - 绿色主题优化版
 * 增强版JavaScript文件，包含动画、交互和性能优化
 */

// 全局配置
const CONFIG = {
    swiper: {
        autoplayDelay: 4000,
        speed: 800,
        pauseOnMouseEnter: true,
        effect: 'slide',
        loop: true
    },
    search: {
        minLength: 1,
        placeholder: '搜索网站内容...',
        debounceDelay: 300
    },
    animations: {
        duration: 600,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        stagger: 100
    },
    performance: {
        lazyLoadOffset: 100,
        throttleDelay: 16
    }
};

// 增强DOM工具函数
const DOM = {
    ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    },

    $(selector) {
        return document.querySelector(selector);
    },

    $$(selector) {
        return document.querySelectorAll(selector);
    },

    // 创建元素
    create(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
        if (content) element.textContent = content;
        return element;
    },

    // 添加类名
    addClass(element, className) {
        if (element) element.classList.add(className);
    },

    // 移除类名
    removeClass(element, className) {
        if (element) element.classList.remove(className);
    },

    // 切换类名
    toggleClass(element, className) {
        if (element) element.classList.toggle(className);
    },

    // 检查元素是否在视口中
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
};

// 增强搜索功能模块
const SearchModule = {
    debounceTimer: null,
    searchHistory: [],

    init() {
        this.bindEvents();
        this.setupSearchContainer();
        this.loadSearchHistory();
        this.setupQuickSearch();
    },

    bindEvents() {
        const searchInput = DOM.$('.search-input');
        const searchButton = DOM.$('.search-button');

        if (!searchInput || !searchButton) return;

        // 搜索按钮点击事件
        searchButton.addEventListener('click', () => this.performSearch());

        // 回车键搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 实时搜索建议
        searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // 搜索框焦点效果
        searchInput.addEventListener('focus', () => this.onSearchFocus());
        searchInput.addEventListener('blur', () => this.onSearchBlur());
    },

    setupSearchContainer() {
        const container = DOM.$('.search-container');
        if (container) {
            container.addEventListener('mouseenter', () => {
                container.style.background = 'rgba(255, 255, 255, 1)';
                container.style.transform = 'translateY(-1px)';
            });

            container.addEventListener('mouseleave', () => {
                if (!DOM.$('.search-input:focus')) {
                    container.style.background = 'rgba(255, 255, 255, 0.95)';
                    container.style.transform = 'translateY(0)';
                }
            });
        }
    },

    setupQuickSearch() {
        const quickSearchTags = DOM.$$('.search-container + div span');
        quickSearchTags.forEach(tag => {
            tag.addEventListener('click', () => {
                const searchInput = DOM.$('.search-input');
                if (searchInput) {
                    searchInput.value = tag.textContent;
                    this.performSearch();
                }
            });
        });
    },

    handleSearchInput(value) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            if (value.length >= CONFIG.search.minLength) {
                this.showSearchSuggestions(value);
            } else {
                this.hideSearchSuggestions();
            }
        }, CONFIG.search.debounceDelay);
    },

    showSearchSuggestions(query) {
        // 模拟搜索建议
        const suggestions = [
            '法学研究',
            '法律服务',
            '通知公告',
            '学会动态',
            '党建工作'
        ].filter(item => item.includes(query));

        if (suggestions.length > 0) {
            this.renderSuggestions(suggestions);
        }
    },

    renderSuggestions(suggestions) {
        let suggestionBox = DOM.$('.search-suggestions');
        if (!suggestionBox) {
            suggestionBox = DOM.create('div', {
                class: 'search-suggestions absolute top-full left-0 right-0 bg-white rounded-lg shadow-xl border border-gray-100 mt-2 z-50 max-h-60 overflow-y-auto'
            });
            DOM.$('.search-container').appendChild(suggestionBox);
        }

        suggestionBox.innerHTML = suggestions.map(suggestion =>
            `<div class="px-4 py-2 hover:bg-green-50 cursor-pointer transition-colors" data-suggestion="${suggestion}">
                <i class="fas fa-search text-green-500 mr-2"></i>${suggestion}
            </div>`
        ).join('');

        // 绑定建议点击事件
        suggestionBox.querySelectorAll('[data-suggestion]').forEach(item => {
            item.addEventListener('click', () => {
                DOM.$('.search-input').value = item.dataset.suggestion;
                this.performSearch();
                this.hideSearchSuggestions();
            });
        });
    },

    hideSearchSuggestions() {
        const suggestionBox = DOM.$('.search-suggestions');
        if (suggestionBox) {
            suggestionBox.remove();
        }
    },

    onSearchFocus() {
        const container = DOM.$('.search-container');
        if (container) {
            DOM.addClass(container, 'focused');
        }
    },

    onSearchBlur() {
        setTimeout(() => {
            const container = DOM.$('.search-container');
            if (container) {
                DOM.removeClass(container, 'focused');
                container.style.background = 'rgba(255, 255, 255, 0.95)';
                container.style.transform = 'translateY(0)';
            }
            this.hideSearchSuggestions();
        }, 200);
    },

    performSearch() {
        const searchInput = DOM.$('.search-input');
        if (!searchInput) return;

        const searchTerm = searchInput.value.trim();
        if (searchTerm.length >= CONFIG.search.minLength) {
            this.addToHistory(searchTerm);
            this.showSearchAnimation();
            console.log('搜索内容:', searchTerm);

            // 模拟搜索结果
            setTimeout(() => {
                this.showSearchResults(searchTerm);
            }, 1000);
        }
    },

    showSearchAnimation() {
        const button = DOM.$('.search-button');
        if (button) {
            const icon = button.querySelector('i');
            icon.className = 'fas fa-spinner fa-spin text-sm';
            setTimeout(() => {
                icon.className = 'fas fa-search text-sm';
            }, 1000);
        }
    },

    showSearchResults(term) {
        // 创建搜索结果提示
        const notification = DOM.create('div', {
            class: 'fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300'
        }, `找到 ${Math.floor(Math.random() * 50) + 1} 条关于"${term}"的结果`);

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(full)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    },

    addToHistory(term) {
        if (!this.searchHistory.includes(term)) {
            this.searchHistory.unshift(term);
            if (this.searchHistory.length > 10) {
                this.searchHistory.pop();
            }
            this.saveSearchHistory();
        }
    },

    loadSearchHistory() {
        const saved = localStorage.getItem('searchHistory');
        if (saved) {
            this.searchHistory = JSON.parse(saved);
        }
    },

    saveSearchHistory() {
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }
};

// 轮播图模块
const SwiperModule = {
    instance: null,

    init() {
        if (typeof Swiper === 'undefined') {
            console.warn('Swiper library not loaded');
            return;
        }

        this.initSwiper();
    },

    initSwiper() {
        const swiperContainer = DOM.$('.swiper');
        if (!swiperContainer) return;

        this.instance = new Swiper('.swiper', {
            slidesPerView: 1,
            spaceBetween: 0,
            centeredSlides: true,
            
            autoplay: {
                delay: CONFIG.swiper.autoplayDelay,
                disableOnInteraction: false,
                pauseOnMouseEnter: CONFIG.swiper.pauseOnMouseEnter,
            },
            
            loop: true,
            effect: 'slide',
            speed: CONFIG.swiper.speed,
            
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                type: 'bullets',
            },
            
            touchRatio: 1,
            touchAngle: 45,
            
            breakpoints: {
                640: { slidesPerView: 1 },
                768: { slidesPerView: 1 },
                1024: { slidesPerView: 1 }
            }
        });
    },

    destroy() {
        if (this.instance) {
            this.instance.destroy(true, true);
            this.instance = null;
        }
    }
};

// 增强平滑滚动模块
const SmoothScrollModule = {
    init() {
        this.bindEvents();
        this.setupScrollToTop();
        this.setupScrollProgress();
    },

    bindEvents() {
        const anchors = DOM.$$('a[href^="#"]');
        anchors.forEach(anchor => {
            anchor.addEventListener('click', (e) => this.handleAnchorClick(e));
        });
    },

    handleAnchorClick(e) {
        e.preventDefault();
        const targetId = e.currentTarget.getAttribute('href');
        const target = DOM.$(targetId);

        if (target) {
            const offsetTop = target.offsetTop - 80; // 考虑固定导航栏高度
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    },

    setupScrollToTop() {
        // 创建回到顶部按钮
        const scrollTopBtn = DOM.create('button', {
            class: 'fixed bottom-6 right-6 w-12 h-12 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-all duration-300 transform scale-0 z-50',
            'aria-label': '回到顶部'
        });
        scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        document.body.appendChild(scrollTopBtn);

        // 滚动事件监听
        let ticking = false;
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    if (scrollTop > 300) {
                        scrollTopBtn.style.transform = 'scale(1)';
                    } else {
                        scrollTopBtn.style.transform = 'scale(0)';
                    }
                    ticking = false;
                });
                ticking = true;
            }
        });

        // 点击回到顶部
        scrollTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    },

    setupScrollProgress() {
        // 创建滚动进度条
        const progressBar = DOM.create('div', {
            class: 'fixed top-0 left-0 h-1 bg-gradient-to-r from-green-400 to-green-600 z-50 transition-all duration-300',
            style: 'width: 0%'
        });
        document.body.appendChild(progressBar);

        // 更新进度条
        let ticking = false;
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
                    const progress = (scrollTop / scrollHeight) * 100;
                    progressBar.style.width = `${Math.min(progress, 100)}%`;
                    ticking = false;
                });
                ticking = true;
            }
        });
    }
};

// 移动端菜单模块
const MobileMenuModule = {
    init() {
        this.bindEvents();
    },

    bindEvents() {
        const menuBtn = DOM.$('.mobile-menu-btn');
        const mobileMenu = DOM.$('.mobile-menu');

        if (menuBtn && mobileMenu) {
            menuBtn.addEventListener('click', () => {
                this.toggleMenu();
            });

            // 点击菜单项后关闭菜单
            const menuItems = mobileMenu.querySelectorAll('a');
            menuItems.forEach(item => {
                item.addEventListener('click', () => {
                    this.closeMenu();
                });
            });
        }
    },

    toggleMenu() {
        const mobileMenu = DOM.$('.mobile-menu');
        const menuBtn = DOM.$('.mobile-menu-btn');

        if (mobileMenu && menuBtn) {
            const isOpen = !mobileMenu.classList.contains('hidden');

            if (isOpen) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        }
    },

    openMenu() {
        const mobileMenu = DOM.$('.mobile-menu');
        const menuBtn = DOM.$('.mobile-menu-btn');

        if (mobileMenu && menuBtn) {
            DOM.removeClass(mobileMenu, 'hidden');
            menuBtn.querySelector('i').className = 'fas fa-times';

            // 添加动画
            mobileMenu.style.maxHeight = '0';
            mobileMenu.style.opacity = '0';

            requestAnimationFrame(() => {
                mobileMenu.style.maxHeight = '400px';
                mobileMenu.style.opacity = '1';
            });
        }
    },

    closeMenu() {
        const mobileMenu = DOM.$('.mobile-menu');
        const menuBtn = DOM.$('.mobile-menu-btn');

        if (mobileMenu && menuBtn) {
            mobileMenu.style.maxHeight = '0';
            mobileMenu.style.opacity = '0';

            setTimeout(() => {
                DOM.addClass(mobileMenu, 'hidden');
                menuBtn.querySelector('i').className = 'fas fa-bars';
            }, 300);
        }
    }
};

// 懒加载模块
const LazyLoadModule = {
    observer: null,

    init() {
        if ('IntersectionObserver' in window) {
            this.setupObserver();
            this.observeImages();
        } else {
            // 降级处理
            this.loadAllImages();
        }
    },

    setupObserver() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.observer.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: `${CONFIG.performance.lazyLoadOffset}px`
        });
    },

    observeImages() {
        const images = DOM.$$('img[data-src]');
        images.forEach(img => {
            this.observer.observe(img);
        });
    },

    loadImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');

            img.addEventListener('load', () => {
                DOM.addClass(img, 'fade-in');
            });
        }
    },

    loadAllImages() {
        const images = DOM.$$('img[data-src]');
        images.forEach(img => this.loadImage(img));
    }
};

// 增强主应用模块
const App = {
    modules: [],

    init() {
        console.log('通辽市法学会网站 - 绿色主题优化版初始化中...');

        // 性能监控
        const startTime = performance.now();

        // 初始化所有模块
        this.initModules();

        // 初始化AOS动画
        this.initAOS();

        // 设置性能优化
        this.setupPerformanceOptimizations();

        // 添加全局事件监听
        this.bindGlobalEvents();

        const endTime = performance.now();
        console.log(`网站初始化完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

        // 显示加载完成提示
        this.showLoadingComplete();
    },

    initModules() {
        const modules = [
            SearchModule,
            SwiperModule,
            SmoothScrollModule,
            MobileMenuModule,
            LazyLoadModule
        ];

        modules.forEach(module => {
            try {
                module.init();
                this.modules.push(module);
                console.log(`${module.constructor.name || 'Module'} 初始化成功`);
            } catch (error) {
                console.error(`模块初始化失败:`, error);
            }
        });
    },

    initAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: CONFIG.animations.duration,
                easing: 'ease-out-cubic',
                once: true,
                offset: 50,
                delay: 0
            });
        }
    },

    setupPerformanceOptimizations() {
        // 预加载关键资源
        this.preloadCriticalResources();

        // 设置图片懒加载
        this.setupImageOptimization();

        // 优化滚动性能
        this.optimizeScrollPerformance();
    },

    preloadCriticalResources() {
        const criticalImages = [
            'images/banner2.jpg',
            'images/s1.jpg',
            'images/s2.png',
            'images/s3.jpg'
        ];

        criticalImages.forEach(src => {
            const link = DOM.create('link', {
                rel: 'preload',
                as: 'image',
                href: src
            });
            document.head.appendChild(link);
        });
    },

    setupImageOptimization() {
        // 为所有图片添加加载优化
        const images = DOM.$$('img');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }

            // 添加错误处理
            img.addEventListener('error', () => {
                img.style.display = 'none';
                console.warn(`图片加载失败: ${img.src}`);
            });
        });
    },

    optimizeScrollPerformance() {
        let ticking = false;

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }, { passive: true });
    },

    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // 导航栏背景透明度
        const nav = DOM.$('nav');
        if (nav) {
            const opacity = Math.min(scrollTop / 100, 1);
            nav.style.backgroundColor = `rgba(22, 163, 74, ${0.9 + opacity * 0.1})`;
        }

        // 视差效果
        const header = DOM.$('header');
        if (header && scrollTop < window.innerHeight) {
            header.style.transform = `translateY(${scrollTop * 0.5}px)`;
        }
    },

    bindGlobalEvents() {
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = DOM.$('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // ESC 关闭搜索建议
            if (e.key === 'Escape') {
                SearchModule.hideSearchSuggestions();
                MobileMenuModule.closeMenu();
            }
        });

        // 窗口大小变化
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });

        // 网络状态监控
        if ('navigator' in window && 'onLine' in navigator) {
            window.addEventListener('online', () => {
                this.showNetworkStatus('网络连接已恢复', 'success');
            });

            window.addEventListener('offline', () => {
                this.showNetworkStatus('网络连接已断开', 'warning');
            });
        }
    },

    handleResize() {
        // 重新初始化AOS
        if (typeof AOS !== 'undefined') {
            AOS.refresh();
        }

        // 重新计算轮播图尺寸
        if (SwiperModule.instance) {
            SwiperModule.instance.update();
        }
    },

    showLoadingComplete() {
        // 隐藏加载动画（如果有的话）
        const loader = DOM.$('.page-loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
        }

        // 显示页面内容
        document.body.style.opacity = '1';
    },

    showNetworkStatus(message, type) {
        const notification = DOM.create('div', {
            class: `fixed top-4 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg shadow-lg z-50 text-white transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 'bg-yellow-500'
            }`
        }, message);

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    },

    destroy() {
        // 清理所有模块
        this.modules.forEach(module => {
            if (module.destroy) {
                module.destroy();
            }
        });

        // 清理AOS
        if (typeof AOS !== 'undefined') {
            AOS.refreshHard();
        }

        console.log('应用已清理');
    }
};

// 页面加载完成后初始化应用
DOM.ready(() => {
    App.init();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    App.destroy();
});

// 错误处理
window.addEventListener('error', (e) => {
    console.error('全局错误:', e.error);
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (e) => {
    console.error('未处理的Promise拒绝:', e.reason);
});
