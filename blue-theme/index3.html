<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通辽市法学会 - 新闻门户版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;900&display=swap');
        
        :root {
            --primary-blue: #0f4c75;
            --secondary-blue: #3282b8;
            --accent-blue: #0099e0;
            --light-blue: #e8f4fd;
            --dark-blue: #0a3a5c;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: #f8fafc;
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--accent-blue) 100%);
            position: relative;
            overflow: hidden;
        }
        
        .hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .glass-nav {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .news-card {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-color);
        }
        
        .news-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--accent-blue);
        }
        
        .news-card-large {
            position: relative;
            overflow: hidden;
        }
        
        .news-card-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.7) 100%);
            z-index: 1;
        }
        
        .news-card-large .card-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 2rem;
            z-index: 2;
            color: white;
        }
        
        .category-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: var(--accent-blue);
            color: white;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .search-modern {
            position: relative;
            max-width: 400px;
        }
        
        .search-modern input {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid transparent;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .search-modern input:focus {
            outline: none;
            border-color: var(--accent-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 153, 224, 0.1);
        }
        
        .search-modern button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border: none;
            background: var(--accent-blue);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-modern button:hover {
            background: var(--secondary-blue);
            transform: translateY(-50%) scale(1.05);
        }
        
        .nav-link {
            position: relative;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: var(--accent-blue);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: var(--accent-blue);
            border-radius: 2px;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-link:hover::after {
            width: 80%;
        }
        
        .section-title {
            position: relative;
            padding-left: 1rem;
            margin-bottom: 2rem;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--accent-blue), var(--secondary-blue));
            border-radius: 2px;
        }
        
        .breaking-news {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--accent-blue);
            margin-bottom: 0.5rem;
        }
        
        .floating-actions {
            position: fixed;
            right: 2rem;
            bottom: 2rem;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .floating-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--accent-blue);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .floating-btn:hover {
            transform: scale(1.1);
            background: var(--secondary-blue);
        }
        
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(0, 153, 224, 0.2);
            z-index: 1000;
        }
        
        .scroll-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--secondary-blue));
            width: 0;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .floating-actions {
                right: 1rem;
                bottom: 1rem;
            }
            
            .hero-gradient {
                padding: 2rem 0;
            }
            
            .search-modern {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 滚动进度条 -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>

    <!-- 顶部导航 -->
    <nav class="glass-nav sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-4">
                <div class="flex items-center space-x-4">
                    <img src="images/logo.png" alt="通辽市法学会" class="h-12 w-auto">
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">通辽市法学会</h1>
                        <p class="text-sm text-gray-600">Tongliao Law Society</p>
                    </div>
                </div>
                
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#" class="nav-link">首页</a>
                    <a href="#" class="nav-link">法学动态</a>
                    <a href="#" class="nav-link">学术研究</a>
                    <a href="#" class="nav-link">法律服务</a>
                    <a href="#" class="nav-link">会员之家</a>
                    <a href="#" class="nav-link">关于我们</a>
                </div>
                
                <div class="search-modern">
                    <input type="text" placeholder="搜索法律资讯..." id="searchInput">
                    <button type="button" id="searchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-gradient py-16">
        <div class="container mx-auto px-4">
            <div class="text-center text-white mb-12">
                <div class="breaking-news mb-6">
                    <i class="fas fa-bolt"></i>
                    <span>最新资讯</span>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-4">
                    推进法治建设
                    <span class="block text-3xl md:text-5xl mt-2 opacity-90">服务社会发展</span>
                </h2>
                <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                    通辽市法学会致力于法学理论研究、法律实务探讨、法治文化传播，为地方法治建设贡献智慧力量
                </p>
            </div>
            
            <!-- 统计数据 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="stats-card">
                    <div class="stats-number">150+</div>
                    <p class="text-gray-600">会员专家</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number">2000+</div>
                    <p class="text-gray-600">法律服务</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number">50+</div>
                    <p class="text-gray-600">学术研究</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number">10+</div>
                    <p class="text-gray-600">服务年限</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-12">
        <!-- 头条新闻 -->
        <section class="mb-16">
            <div class="section-title">
                <h2 class="text-3xl font-bold text-gray-800">头条新闻</h2>
                <p class="text-gray-600 mt-2">最新法学动态与重要资讯</p>
            </div>
            
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- 主要新闻 -->
                <div class="lg:col-span-2">
                    <div class="news-card-large h-96 rounded-2xl overflow-hidden">
                        <img src="images/banner1.jpg" alt="头条新闻" class="w-full h-full object-cover">
                        <div class="card-content">
                            <div class="category-badge mb-3">重要新闻</div>
                            <h3 class="text-2xl font-bold mb-3">通辽市法学会举办法治建设专题研讨会</h3>
                            <p class="text-gray-200 mb-4">
                                为深入贯彻习近平法治思想，推进地方法治建设，通辽市法学会邀请多位知名法学专家，就新时代法治建设的重点难点问题进行深入研讨...
                            </p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">2024年7月6日</span>
                                <a href="#" class="text-blue-200 hover:text-white transition-colors">
                                    阅读全文 <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 侧边新闻 -->
                <div class="space-y-6">
                    <div class="news-card p-6">
                        <div class="category-badge mb-3">学术研究</div>
                        <h4 class="text-lg font-semibold mb-3">民法典实施中的疑难问题研究</h4>
                        <p class="text-gray-600 text-sm mb-4">
                            针对民法典实施过程中出现的新问题，法学会组织专家进行深入研究...
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">2024年7月5日</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">详情</a>
                        </div>
                    </div>
                    
                    <div class="news-card p-6">
                        <div class="category-badge mb-3">法律服务</div>
                        <h4 class="text-lg font-semibold mb-3">免费法律咨询服务月启动</h4>
                        <p class="text-gray-600 text-sm mb-4">
                            为更好地服务群众，法学会将在本月开展免费法律咨询服务活动...
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">2024年7月4日</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">详情</a>
                        </div>
                    </div>
                    
                    <div class="news-card p-6">
                        <div class="category-badge mb-3">会员动态</div>
                        <h4 class="text-lg font-semibold mb-3">新会员入会仪式圆满举行</h4>
                        <p class="text-gray-600 text-sm mb-4">
                            20余名法律从业者正式加入通辽市法学会，为学会注入新活力...
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">2024年7月3日</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">详情</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 专题栏目 -->
        <section class="mb-16">
            <div class="section-title">
                <h2 class="text-3xl font-bold text-gray-800">专题栏目</h2>
                <p class="text-gray-600 mt-2">深度解析法学理论与实务</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="news-card overflow-hidden">
                    <img src="images/s1.jpg" alt="法学理论" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="category-badge mb-3">理论研究</div>
                        <h3 class="text-xl font-semibold mb-3">新时代法学理论创新</h3>
                        <p class="text-gray-600 mb-4">
                            探索中国特色社会主义法学理论体系，推进法学理论与实践相结合...
                        </p>
                        <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">
                            了解更多 <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                
                <div class="news-card overflow-hidden">
                    <img src="images/s2.png" alt="法律实务" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="category-badge mb-3">实务探讨</div>
                        <h3 class="text-xl font-semibold mb-3">法律实务前沿问题</h3>
                        <p class="text-gray-600 mb-4">
                            关注司法实践中的热点难点问题，为法律从业者提供专业指导...
                        </p>
                        <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">
                            了解更多 <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                
                <div class="news-card overflow-hidden">
                    <img src="images/s3.jpg" alt="法治文化" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="category-badge mb-3">文化传播</div>
                        <h3 class="text-xl font-semibold mb-3">法治文化建设</h3>
                        <p class="text-gray-600 mb-4">
                            弘扬法治精神，传播法治文化，提升全民法治素养...
                        </p>
                        <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">
                            了解更多 <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 快讯滚动 -->
        <section class="mb-16">
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
                <div class="flex items-center mb-6">
                    <i class="fas fa-newspaper text-2xl text-blue-600 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">法学快讯</h2>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between py-4 border-b border-gray-100">
                        <div class="flex items-center space-x-4">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-gray-800">最高法院发布新司法解释，涉及民事诉讼程序</span>
                        </div>
                        <span class="text-sm text-gray-500">刚刚</span>
                    </div>
                    
                    <div class="flex items-center justify-between py-4 border-b border-gray-100">
                        <div class="flex items-center space-x-4">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-800">通辽市法学会与高校合作共建法学实践基地</span>
                        </div>
                        <span class="text-sm text-gray-500">2小时前</span>
                    </div>
                    
                    <div class="flex items-center justify-between py-4 border-b border-gray-100">
                        <div class="flex items-center space-x-4">
                            <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <span class="text-gray-800">全国法学会工作会议在京召开</span>
                        </div>
                        <span class="text-sm text-gray-500">5小时前</span>
                    </div>
                    
                    <div class="flex items-center justify-between py-4">
                        <div class="flex items-center space-x-4">
                            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <span class="text-gray-800">法治建设专题培训班开班</span>
                        </div>
                        <span class="text-sm text-gray-500">1天前</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部 -->
    <footer class="hero-gradient text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">通辽市法学会</h3>
                    <p class="text-gray-200 mb-4">
                        致力于法学理论研究、法律实务探讨、法治文化传播
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-200 hover:text-white transition-colors">
                            <i class="fab fa-weixin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-200 hover:text-white transition-colors">
                            <i class="fab fa-weibo text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">学会概况</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">组织机构</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">章程制度</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">会员服务</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">服务项目</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">法律咨询</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">学术研究</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">培训教育</a></li>
                        <li><a href="#" class="text-gray-200 hover:text-white transition-colors">法治宣传</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">联系我们</h4>
                    <div class="space-y-2 text-gray-200">
                        <p><i class="fas fa-map-marker-alt mr-2"></i>通辽市科尔沁区</p>
                        <p><i class="fas fa-phone mr-2"></i>0475-1234567</p>
                        <p><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-600 mt-8 pt-8 text-center text-gray-200">
                <p>&copy; 2024 通辽市法学会. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 浮动按钮 -->
    <div class="floating-actions">
        <button class="floating-btn" onclick="scrollToTop()" title="回到顶部">
            <i class="fas fa-chevron-up"></i>
        </button>
        <button class="floating-btn" onclick="toggleDarkMode()" title="切换主题">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        // 滚动进度条
        window.addEventListener('scroll', function() {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('scrollProgress').style.width = scrolled + '%';
        });

        // 回到顶部
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 切换主题（简单实现）
        function toggleDarkMode() {
            // 这里可以实现暗色主题切换
            console.log('切换主题功能待实现');
        }

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('searchInput').value;
            if (searchTerm.trim()) {
                console.log('搜索:', searchTerm);
                // 实际搜索逻辑
            }
        });

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('searchBtn').click();
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // 动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有新闻卡片
        document.querySelectorAll('.news-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>